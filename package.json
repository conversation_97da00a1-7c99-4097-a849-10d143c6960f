{"name": "sportstake-app-backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "sync-sports": "ts-node src/jobs/syncSGOSports.ts", "sync-leagues": "ts-node src/jobs/syncSGOLeagues.ts", "sync-teams": "ts-node src/jobs/syncSGOTeams.ts", "sync-teams-league": "ts-node src/jobs/syncSGOTeams.ts", "sync-players": "ts-node src/jobs/syncSGOPlayers.ts", "sync-players-team": "ts-node src/jobs/syncSGOPlayers.ts", "sync-events": "ts-node src/jobs/syncSGOEvents.ts", "sync-events-league": "ts-node src/jobs/syncSGOEvents.ts"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.14", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "private": true, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@supabase/supabase-js": "^2.51.0", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0"}}
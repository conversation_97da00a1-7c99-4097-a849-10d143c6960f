import dotenv from 'dotenv';
import { syncSGOSports } from './jobs/syncSGOSports';
import { syncSGOLeagues } from './jobs/syncSGOLeagues';
import { syncSGOTeams } from './jobs/syncSGOTeams';

// Load environment variables
dotenv.config();

/**
 * Main application entry point
 * Initializes the backend and runs the SGO sports sync job
 */

async function main(): Promise<void> {
  try {
    console.log('🚀 SportStake Backend Starting...');

    // Validate required environment variables
    const requiredEnvVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY', 'SGO_API_KEY'];
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

    if (missingEnvVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
    }

    console.log('✅ Environment variables validated');

    // Check if sync is enabled on startup
    const enableSyncOnStartup = process.env.ENABLE_SYNC_ON_STARTUP === 'true';

    if (enableSyncOnStartup) {
      console.log('🔄 Starting SGO data synchronization...');

      // Run sports sync if enabled
      if (process.env.ENABLE_SPORTS_SYNC !== 'false') {
        console.log('📊 Syncing sports data...');
        await syncSGOSports();
        console.log('✅ Sports synchronization completed');
      } else {
        console.log('⏭️  Sports sync disabled');
      }

      // Run leagues sync if enabled
      if (process.env.ENABLE_LEAGUES_SYNC !== 'false') {
        console.log('🏆 Syncing leagues data...');
        await syncSGOLeagues();
        console.log('✅ Leagues synchronization completed');
      } else {
        console.log('⏭️  Leagues sync disabled');
      }

      // Run teams sync if enabled
      if (process.env.ENABLE_TEAMS_SYNC !== 'false') {
        console.log('👥 Syncing teams data...');
        await syncSGOTeams();
        console.log('✅ Teams synchronization completed');
      } else {
        console.log('⏭️  Teams sync disabled');
      }

      console.log('✅ All enabled sync jobs completed successfully');
    } else {
      console.log('⏭️  Sync on startup disabled (ENABLE_SYNC_ON_STARTUP=false)');
      console.log('💡 Run individual sync jobs manually:');
      console.log('   npm run sync-sports');
      console.log('   npm run sync-leagues');
      console.log('   npm run sync-teams');
    }

    console.log('🎉 SportStake Backend initialized successfully!');

  } catch (error) {
    console.error('❌ Failed to initialize SportStake Backend:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Start the application
main();

import { fetchLeagues, SGOLeague } from '../api/sportsgameodds/fetchLeagues';
import { supabase } from '../db/supabaseClient';

// Interface for the leagues data that matches the sgo_leagues table schema
export interface SGOLeaguesTableRow {
  leagueid: string;
  sportid: string;
  enabled: boolean;
  name: string;
  shortname: string;
  updatedat?: string;
}

/**
 * Transforms SGO API league data to match the sgo_leagues table schema
 * Uses exact field names from the table
 */
function transformLeagueData(league: SGOLeague): SGOLeaguesTableRow {
  return {
    leagueid: league.leagueID,
    sportid: league.sportID,
    enabled: league.enabled,
    name: league.name,
    shortname: league.shortName,
    updatedat: new Date().toISOString()
  };
}

/**
 * Synchronizes leagues data from SGO API to the sgo_leagues table
 * Fetches data from API, transforms it, and upserts to Supabase
 */
export async function syncSGOLeagues(): Promise<void> {
  try {
    // Check if leagues sync is enabled
    if (process.env.ENABLE_LEAGUES_SYNC === 'false') {
      console.log('⏭️  Leagues sync disabled (ENABLE_LEAGUES_SYNC=false)');
      return;
    }

    console.log('Starting SGO leagues synchronization...');

    // Fetch leagues data from SGO API
    const leaguesResponse = await fetchLeagues();
    
    if (!leaguesResponse.data || leaguesResponse.data.length === 0) {
      console.log('No leagues data received from SGO API');
      return;
    }

    // Transform the data to match our table schema
    const transformedLeagues = leaguesResponse.data.map(transformLeagueData);
    
    console.log(`Transformed ${transformedLeagues.length} leagues records for database insertion`);

    // Upsert data to Supabase (insert or update if leagueid already exists)
    const { data, error } = await supabase
      .from('sgo_leagues')
      .upsert(transformedLeagues, {
        onConflict: 'leagueid',
        ignoreDuplicates: false
      })
      .select();

    if (error) {
      throw new Error(`Supabase upsert failed: ${error.message}`);
    }

    console.log(`Successfully synchronized ${data?.length || transformedLeagues.length} leagues records to database`);
    
    // Log some details about what was synced
    const leagueIds = transformedLeagues.map(l => l.leagueid).join(', ');
    console.log(`Synced leagues: ${leagueIds}`);

  } catch (error) {
    console.error('SGO leagues synchronization failed:', error);
    throw error;
  }
}

export default syncSGOLeagues;

// Allow this file to be run directly as a script
if (require.main === module) {
  syncSGOLeagues()
    .then(() => {
      console.log('✅ Leagues sync completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Leagues sync failed:', error);
      process.exit(1);
    });
}

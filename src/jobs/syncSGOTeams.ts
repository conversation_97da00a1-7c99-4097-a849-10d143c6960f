import { fetchAllTeamsForLeague, SGOTeam } from '../api/sportsgameodds/fetchTeams';
import { supabase } from '../db/supabaseClient';

// Interface for the teams data that matches the sgo_teams table schema
export interface SGOTeamsTableRow {
  teamid: string;
  sportid: string;
  leagueid: string;
  shortname: string;
  mediumname: string;
  longname: string;
  nickname: string;
  location: string;
  updatedat?: string;
}

/**
 * Transforms SGO API team data to match the sgo_teams table schema
 * Uses exact field names from the table with fallback logic for missing names
 */
function transformTeamData(team: SGOTeam): SGOTeamsTableRow {
  return {
    teamid: team.teamID,
    sportid: team.sportID,
    leagueid: team.leagueID,
    shortname: team.names.short || team.names.medium || team.names.long || 'UNKNOWN',
    mediumname: team.names.medium || team.names.long || team.names.short || 'UNKNOWN',
    longname: team.names.long || team.names.medium || team.names.short || 'UNKNOWN',
    nickname: team.nickname || '',
    location: team.location || '',
    updatedat: new Date().toISOString()
  };
}

/**
 * Fetches all enabled leagues from the sgo_leagues table
 * @returns Promise<Array<{leagueid: string, sportid: string}>> - Array of league info
 */
async function getEnabledLeagues(): Promise<Array<{leagueid: string, sportid: string}>> {
  const { data, error } = await supabase
    .from('sgo_leagues')
    .select('leagueid, sportid')
    .eq('enabled', true);

  if (error) {
    throw new Error(`Failed to fetch leagues from database: ${error.message}`);
  }

  return data || [];
}

/**
 * Synchronizes teams data from SGO API to the sgo_teams table
 * Fetches enabled leagues from database, then fetches teams for each league
 * @param targetLeagueID - Optional league ID to sync teams for only that league
 */
export async function syncSGOTeams(targetLeagueID?: string): Promise<void> {
  try {
    // Check if teams sync is enabled
    if (process.env.ENABLE_TEAMS_SYNC === 'false') {
      console.log('⏭️  Teams sync disabled (ENABLE_TEAMS_SYNC=false)');
      return;
    }

    console.log('Starting SGO teams synchronization...');

    // Get all enabled leagues from the database
    let leagues = await getEnabledLeagues();

    if (leagues.length === 0) {
      console.log('No enabled leagues found in database. Run syncSGOLeagues first.');
      return;
    }

    // Filter to target league if specified
    if (targetLeagueID) {
      console.log(`⚾ Targeting league: ${targetLeagueID}`);
      leagues = leagues.filter(league => league.leagueid === targetLeagueID);

      if (leagues.length === 0) {
        console.log(`❌ Target league '${targetLeagueID}' not found in enabled leagues`);
        return;
      }

      console.log(`Found target league: ${leagues[0].leagueid} (${leagues[0].sportid})`);
    } else {
      console.log(`Found ${leagues.length} enabled leagues to sync teams for`);
    }

    let totalTeamsSynced = 0;
    const allTransformedTeams: SGOTeamsTableRow[] = [];

    // Process each league
    for (const league of leagues) {
      try {
        console.log(`Processing teams for league: ${league.leagueid} (${league.sportid})`);
        
        // Fetch all teams for this league (handles pagination automatically)
        const teams = await fetchAllTeamsForLeague(league.leagueid);
        
        if (teams.length === 0) {
          console.log(`No teams found for league ${league.leagueid}`);
          continue;
        }

        // Transform the teams data
        const transformedTeams = teams.map(transformTeamData);
        allTransformedTeams.push(...transformedTeams);
        
        console.log(`Processed ${transformedTeams.length} teams for league ${league.leagueid}`);
        totalTeamsSynced += transformedTeams.length;

      } catch (error) {
        console.error(`Failed to process teams for league ${league.leagueid}:`, error);
        // Continue with other leagues even if one fails
        continue;
      }
    }

    if (allTransformedTeams.length === 0) {
      console.log('No teams data to sync');
      return;
    }

    console.log(`Transformed ${allTransformedTeams.length} teams records for database insertion`);

    // Upsert all teams data to Supabase (insert or update if teamid already exists)
    const { data, error } = await supabase
      .from('sgo_teams')
      .upsert(allTransformedTeams, {
        onConflict: 'teamid',
        ignoreDuplicates: false
      })
      .select();

    if (error) {
      throw new Error(`Supabase upsert failed: ${error.message}`);
    }

    console.log(`Successfully synchronized ${data?.length || allTransformedTeams.length} teams records to database`);
    console.log(`Total teams processed across all leagues: ${totalTeamsSynced}`);

  } catch (error) {
    console.error('SGO teams synchronization failed:', error);
    throw error;
  }
}

export default syncSGOTeams;

// Allow this file to be run directly as a script
if (require.main === module) {
  // Get target league from command line arguments
  const targetLeagueID = process.argv[2];

  syncSGOTeams(targetLeagueID)
    .then(() => {
      console.log('✅ Teams sync completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Teams sync failed:', error);
      process.exit(1);
    });
}

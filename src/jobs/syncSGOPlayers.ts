import { fetchPlayers, SGOPlayer } from '../api/sportsgameodds/fetchPlayers';
import { supabase } from '../db/supabaseClient';

// Interface for the players data that matches the sgo_players table schema
export interface SGOPlayersTableRow {
  playerid: string;
  firstname: string;
  lastname: string;
  name: string;
  sportid: string;
  leagueid: string;
  teamid: string;
  position: string;
  jerseynumber: number | null;
  updatedat?: string;
}

/**
 * Transforms SGO API player data to match the sgo_players table schema
 * Uses exact field names from the table with fallback logic for missing fields
 */
function transformPlayerData(player: SGOPlayer): SGOPlayersTableRow {
  return {
    playerid: player.playerID,
    firstname: player.firstName || '',
    lastname: player.lastName || '',
    name: player.name || `${player.firstName || ''} ${player.lastName || ''}`.trim() || 'UNKNOWN',
    sportid: player.sportID,
    leagueid: player.leagueID,
    teamid: player.teamID,
    position: player.position || '',
    jerseynumber: player.jerseyNumber || null,
    updatedat: new Date().toISOString()
  };
}

/**
 * Fetches all MLB teams from the sgo_teams table
 * @param targetTeamID - Optional team ID to filter to a specific team
 * @returns Promise<Array<{teamid: string, sportid: string, leagueid: string}>> - Array of team info
 */
async function getMLBTeams(targetTeamID?: string): Promise<Array<{teamid: string, sportid: string, leagueid: string}>> {
  let query = supabase
    .from('sgo_teams')
    .select('teamid, sportid, leagueid')
    .eq('leagueid', 'MLB');

  if (targetTeamID) {
    query = query.eq('teamid', targetTeamID);
  }

  const { data, error } = await query;

  if (error) {
    throw new Error(`Failed to fetch MLB teams from database: ${error.message}`);
  }

  return data || [];
}

/**
 * Synchronizes players data from SGO API to the sgo_players table
 * Fetches MLB teams from database, then fetches players for each team
 * @param targetTeamID - Optional team ID to sync players for only that team
 */
export async function syncSGOPlayers(targetTeamID?: string): Promise<void> {
  try {
    // Check if players sync is enabled
    if (process.env.ENABLE_PLAYERS_SYNC === 'false') {
      console.log('⏭️  Players sync disabled (ENABLE_PLAYERS_SYNC=false)');
      return;
    }

    console.log('Starting SGO players synchronization...');

    // Configuration for testing - can be overridden by parameter
    const configTargetTeamID = targetTeamID || process.env.TARGET_TEAM_ID || undefined;

    // Get all MLB teams from the database
    const teams = await getMLBTeams(configTargetTeamID);
    
    if (teams.length === 0) {
      if (configTargetTeamID) {
        console.log(`No MLB team found with ID '${configTargetTeamID}' in database.`);
      } else {
        console.log('No MLB teams found in database. Run syncSGOTeams first.');
      }
      return;
    }

    // Log targeting information
    if (configTargetTeamID) {
      console.log(`⚾ Targeting team: ${configTargetTeamID}`);
      console.log(`Found target team: ${teams[0].teamid} (${teams[0].sportid}/${teams[0].leagueid})`);
    } else {
      console.log(`Found ${teams.length} MLB teams to sync players for`);
    }

    let totalPlayersSynced = 0;
    const allTransformedPlayers: SGOPlayersTableRow[] = [];

    // Process each team
    for (const team of teams) {
      try {
        console.log(`Processing players for team: ${team.teamid} (${team.leagueid})`);
        
        // Fetch all players for this team
        const playersResponse = await fetchPlayers(team.teamid);
        
        if (!playersResponse.data || playersResponse.data.length === 0) {
          console.log(`No players found for team ${team.teamid}`);
          continue;
        }

        // Transform the players data
        const transformedPlayers = playersResponse.data.map(transformPlayerData);
        allTransformedPlayers.push(...transformedPlayers);
        
        console.log(`Processed ${transformedPlayers.length} players for team ${team.teamid}`);
        totalPlayersSynced += transformedPlayers.length;

      } catch (error) {
        console.error(`Failed to process players for team ${team.teamid}:`, error);
        // Continue with other teams even if one fails
        continue;
      }
    }

    if (allTransformedPlayers.length === 0) {
      console.log('No players data to sync');
      return;
    }

    console.log(`Transformed ${allTransformedPlayers.length} players records for database insertion`);

    // Upsert all players data to Supabase (insert or update if playerid already exists)
    const { data, error } = await supabase
      .from('sgo_players')
      .upsert(allTransformedPlayers, {
        onConflict: 'playerid',
        ignoreDuplicates: false
      })
      .select();

    if (error) {
      throw new Error(`Supabase upsert failed: ${error.message}`);
    }

    console.log(`Successfully synchronized ${data?.length || allTransformedPlayers.length} players records to database`);
    console.log(`Total players processed across all teams: ${totalPlayersSynced}`);

  } catch (error) {
    console.error('SGO players synchronization failed:', error);
    throw error;
  }
}

export default syncSGOPlayers;

// Allow this file to be run directly as a script
if (require.main === module) {
  // Get target team from command line arguments
  const targetTeamID = process.argv[2];

  syncSGOPlayers(targetTeamID)
    .then(() => {
      console.log('✅ Players sync completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Players sync failed:', error);
      process.exit(1);
    });
}

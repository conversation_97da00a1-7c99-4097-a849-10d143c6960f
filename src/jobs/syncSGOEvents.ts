import { fetchAllEvents, SGOEvent } from '../api/sportsgameodds/fetchEvents';
import { supabase } from '../db/supabaseClient';
import { createHash } from 'crypto';

// Interface for the events table schema
export interface SGOEventsTableRow {
  id: string;
  sport_id: string;
  league_id: string;
  type: string;
  starts_at: string;
  status_code: string;
  is_live: boolean;
  is_final: boolean;
  is_cancelled: boolean;
  odds_present: boolean;
  odds_available: boolean;
  current_period?: string;
  previous_period?: string;
  season_id?: string;
  season_week?: string;
  raw: any;
  created_at?: string;
  updated_at?: string;
}

// Interface for the event_participants table schema
export interface SGOEventParticipantsTableRow {
  event_id: string;
  participant_kind: string;
  participant_id: string;
  role: string;
  score?: number;
  extra?: any;
  created_at?: string;
  updated_at?: string;
}

// Interface for the event_period_scores table schema
export interface SGOEventPeriodScoresTableRow {
  event_id: string;
  period_id: string;
  home_points?: number;
  away_points?: number;
  created_at?: string;
  updated_at?: string;
}

// Interface for the odds_markets table schema
export interface SGOOddsMarketsTableRow {
  id: string;
  event_id: string;
  market_name: string;
  stat_id?: string;
  stat_entity_id?: string;
  period_id?: string;
  bet_type_id?: string;
  scoring_supported: boolean;
  created_at?: string;
  updated_at?: string;
}

// Interface for the odds_selections table schema
export interface SGOOddsSelectionsTableRow {
  id: string;
  market_id: string;
  side_id?: string;
  player_id?: string;
  team_id?: string;
  book_odds_available: boolean;
  fair_odds_available: boolean;
  book_odds?: string;
  fair_odds?: string;
  book_over_under?: number;
  fair_over_under?: number;
  started: boolean;
  ended: boolean;
  cancelled: boolean;
  score?: string;
  book_info?: any;
  created_at?: string;
  updated_at?: string;
}

/**
 * Derives status code from SGO event status fields
 */
function deriveStatusCode(status: SGOEvent['status']): string {
  if (status.cancelled) return 'cancelled';
  if (status.delayed) return 'delayed';
  if (status.finalized || status.displayShort === 'F' || status.displayShort === 'FT') return 'final';
  if (status.live) return 'live';
  return 'scheduled';
}

/**
 * Creates a deterministic market ID from market components
 */
function createMarketId(eventId: string, statID: string, statEntityID: string, periodID: string, betTypeID: string): string {
  const input = `${eventId}|${statID}|${statEntityID}|${periodID}|${betTypeID}`;
  return createHash('sha1').update(input).digest('hex');
}

/**
 * Transforms SGO API event data to match the events table schema
 */
function transformEventData(event: SGOEvent): SGOEventsTableRow {
  return {
    id: event.eventID,
    sport_id: event.sportID,
    league_id: event.leagueID,
    type: event.type,
    starts_at: event.status.startsAt,
    status_code: deriveStatusCode(event.status),
    is_live: event.status.live || false,
    is_final: event.status.finalized || event.status.displayShort === 'F' || event.status.displayShort === 'FT' || false,
    is_cancelled: event.status.cancelled || false,
    odds_present: event.status.oddsPresent || false,
    odds_available: event.status.oddsAvailable || false,
    current_period: event.status.currentPeriodID || undefined,
    previous_period: event.status.previousPeriodID || undefined,
    season_id: event.info?.seasonID || undefined,
    season_week: event.info?.seasonWeek || undefined,
    raw: event,
    updated_at: new Date().toISOString()
  };
}

/**
 * Transforms SGO API event data to event participants
 */
function transformEventParticipants(event: SGOEvent): SGOEventParticipantsTableRow[] {
  const participants: SGOEventParticipantsTableRow[] = [];
  
  if (event.teams?.home) {
    participants.push({
      event_id: event.eventID,
      participant_kind: 'team',
      participant_id: event.teams.home.teamID,
      role: 'home',
      score: event.teams.home.score || undefined,
      extra: {
        names: event.teams.home.names,
        colors: event.teams.home.colors
      },
      updated_at: new Date().toISOString()
    });
  }
  
  if (event.teams?.away) {
    participants.push({
      event_id: event.eventID,
      participant_kind: 'team',
      participant_id: event.teams.away.teamID,
      role: 'away',
      score: event.teams.away.score || undefined,
      extra: {
        names: event.teams.away.names,
        colors: event.teams.away.colors
      },
      updated_at: new Date().toISOString()
    });
  }
  
  return participants;
}

/**
 * Transforms SGO API event data to period scores
 */
function transformEventPeriodScores(event: SGOEvent): SGOEventPeriodScoresTableRow[] {
  const periodScores: SGOEventPeriodScoresTableRow[] = [];
  
  if (!event.results) return periodScores;
  
  for (const [periodId, result] of Object.entries(event.results)) {
    // Skip player-specific results
    if (periodId.includes('player') || typeof result !== 'object' || !result) continue;
    
    const homePoints = result.home?.points;
    const awayPoints = result.away?.points;
    
    if (homePoints !== undefined || awayPoints !== undefined) {
      periodScores.push({
        event_id: event.eventID,
        period_id: periodId,
        home_points: homePoints || undefined,
        away_points: awayPoints || undefined,
        updated_at: new Date().toISOString()
      });
    }
  }
  
  return periodScores;
}

/**
 * Transforms SGO API event data to odds markets and selections
 */
function transformOddsData(event: SGOEvent): { markets: SGOOddsMarketsTableRow[]; selections: SGOOddsSelectionsTableRow[] } {
  const markets: SGOOddsMarketsTableRow[] = [];
  const selections: SGOOddsSelectionsTableRow[] = [];

  if (!event.odds) return { markets, selections };

  for (const [oddID, odd] of Object.entries(event.odds)) {
    if (!odd || typeof odd !== 'object') continue;

    const marketId = createMarketId(
      event.eventID,
      odd.statID || '',
      odd.statEntityID || '',
      odd.periodID || '',
      odd.betTypeID || ''
    );

    // Add market
    markets.push({
      id: marketId,
      event_id: event.eventID,
      market_name: odd.marketName || '',
      stat_id: odd.statID || undefined,
      stat_entity_id: odd.statEntityID || undefined,
      period_id: odd.periodID || undefined,
      bet_type_id: odd.betTypeID || undefined,
      scoring_supported: odd.scoringSupported || false,
      updated_at: new Date().toISOString()
    });

    // Add selection
    selections.push({
      id: oddID,
      market_id: marketId,
      side_id: odd.sideID || undefined,
      player_id: odd.playerID || undefined,
      team_id: odd.teamID || undefined,
      book_odds_available: odd.bookOddsAvailable || false,
      fair_odds_available: odd.fairOddsAvailable || false,
      book_odds: odd.bookOdds || undefined,
      fair_odds: odd.fairOdds || undefined,
      book_over_under: odd.bookOverUnder || undefined,
      fair_over_under: odd.fairOverUnder || undefined,
      started: odd.started || false,
      ended: odd.ended || false,
      cancelled: odd.cancelled || false,
      score: odd.score || undefined,
      book_info: odd.byBookmaker || undefined,
      updated_at: new Date().toISOString()
    });
  }

  return { markets, selections };
}

/**
 * Synchronizes events data from SGO API to the database tables
 * @param targetLeagueID - Optional league ID to sync events for only that league
 * @param params - Additional query parameters for the API call
 */
export async function syncSGOEvents(targetLeagueID?: string, params?: {
  sportID?: string;
  oddsAvailable?: boolean;
  limit?: number;
  startsAtGte?: string;
  startsAtLte?: string;
}): Promise<void> {
  try {
    // Check if events sync is enabled
    if (process.env.ENABLE_EVENTS_SYNC === 'false') {
      console.log('⏭️  Events sync disabled (ENABLE_EVENTS_SYNC=false)');
      return;
    }

    console.log('Starting SGO events synchronization...');

    // Build API parameters
    const apiParams = {
      ...params,
      leagueID: targetLeagueID
    };

    if (targetLeagueID) {
      console.log(`🏆 Targeting league: ${targetLeagueID}`);
    }

    // Fetch events data from SGO API
    const events = await fetchAllEvents(apiParams);

    if (events.length === 0) {
      console.log('No events data received from SGO API');
      return;
    }

    console.log(`Processing ${events.length} events for database insertion`);

    // Transform all data
    const transformedEvents = events.map(transformEventData);
    const allParticipants: SGOEventParticipantsTableRow[] = [];
    const allPeriodScores: SGOEventPeriodScoresTableRow[] = [];
    const allMarkets: SGOOddsMarketsTableRow[] = [];
    const allSelections: SGOOddsSelectionsTableRow[] = [];

    for (const event of events) {
      const participants = transformEventParticipants(event);
      const periodScores = transformEventPeriodScores(event);
      const { markets, selections } = transformOddsData(event);

      allParticipants.push(...participants);
      allPeriodScores.push(...periodScores);
      allMarkets.push(...markets);
      allSelections.push(...selections);
    }

    // Upsert events
    if (transformedEvents.length > 0) {
      const { error: eventsError } = await supabase
        .from('events')
        .upsert(transformedEvents, {
          onConflict: 'id',
          ignoreDuplicates: false
        });

      if (eventsError) {
        throw new Error(`Events upsert failed: ${eventsError.message}`);
      }
      console.log(`✅ Upserted ${transformedEvents.length} events`);
    }

    // Upsert event participants
    if (allParticipants.length > 0) {
      const { error: participantsError } = await supabase
        .from('event_participants')
        .upsert(allParticipants, {
          onConflict: 'event_id,participant_kind,participant_id,role',
          ignoreDuplicates: false
        });

      if (participantsError) {
        throw new Error(`Event participants upsert failed: ${participantsError.message}`);
      }
      console.log(`✅ Upserted ${allParticipants.length} event participants`);
    }

    // Upsert period scores
    if (allPeriodScores.length > 0) {
      const { error: periodScoresError } = await supabase
        .from('event_period_scores')
        .upsert(allPeriodScores, {
          onConflict: 'event_id,period_id',
          ignoreDuplicates: false
        });

      if (periodScoresError) {
        throw new Error(`Event period scores upsert failed: ${periodScoresError.message}`);
      }
      console.log(`✅ Upserted ${allPeriodScores.length} period scores`);
    }

    // Upsert odds markets
    if (allMarkets.length > 0) {
      const { error: marketsError } = await supabase
        .from('odds_markets')
        .upsert(allMarkets, {
          onConflict: 'id',
          ignoreDuplicates: false
        });

      if (marketsError) {
        throw new Error(`Odds markets upsert failed: ${marketsError.message}`);
      }
      console.log(`✅ Upserted ${allMarkets.length} odds markets`);
    }

    // Upsert odds selections
    if (allSelections.length > 0) {
      const { error: selectionsError } = await supabase
        .from('odds_selections')
        .upsert(allSelections, {
          onConflict: 'id',
          ignoreDuplicates: false
        });

      if (selectionsError) {
        throw new Error(`Odds selections upsert failed: ${selectionsError.message}`);
      }
      console.log(`✅ Upserted ${allSelections.length} odds selections`);
    }

    console.log(`🎉 Successfully synchronized ${events.length} events with all related data`);

  } catch (error) {
    console.error('SGO events synchronization failed:', error);
    throw error;
  }
}

export default syncSGOEvents;

// Allow this file to be run directly as a script
if (require.main === module) {
  // Get parameters from command line arguments
  const targetLeagueID = process.argv[2];
  const sportID = process.argv[3];
  const oddsAvailable = process.argv.includes('--odds-available');

  const params = {
    ...(sportID && { sportID }),
    ...(oddsAvailable && { oddsAvailable: true })
  };

  syncSGOEvents(targetLeagueID, Object.keys(params).length > 0 ? params : undefined)
    .then(() => {
      console.log('✅ Events sync completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Events sync failed:', error);
      process.exit(1);
    });
}

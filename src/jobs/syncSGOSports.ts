import { fetchSports, SGOSport } from '../api/sportsgameodds/fetchSports';
import { supabase } from '../db/supabaseClient';

// Interface for the flattened sports data that matches the sgo_sports table schema
export interface SGOSportsTableRow {
  sportID: string;
  enabled: boolean;
  hasMeaningfulHomeAway: boolean;
  name: string;
  shortName: string;
  prefers3WayMoneyline?: boolean;
  pointWordShortSingular: string;
  pointWordShortPlural: string;
  pointWordLongSingular: string;
  pointWordLongPlural: string;
  eventWordShortSingular: string;
  eventWordShortPlural: string;
  eventWordLongSingular: string;
  eventWordLongPlural: string;
  defaultPopularityScore: number;
  clockType: string;
  basePeriods: string[]; // JSON array
  extraPeriods: string[]; // JSON array
  unsupportedPeriods: string[]; // JSON array
  updatedAt?: string;
}

/**
 * Transforms SGO API sport data to match the sgo_sports table schema
 * Flattens nested objects and uses exact field names from the table
 */
function transformSportData(sport: SGOSport): Record<string, any> {
  return {
    sportid: sport.sportID,
    enabled: sport.enabled,
    hasmeaningfulhomeaway: sport.hasMeaningfulHomeAway,
    name: sport.name,
    shortname: sport.shortName,
    prefers3waymoneyline: sport.prefers3WayMoneyline || false,
    pointwordshortsingular: sport.pointWord.short.singular,
    pointwordshortplural: sport.pointWord.short.plural,
    pointwordlongsingular: sport.pointWord.long.singular,
    pointwordlongplural: sport.pointWord.long.plural,
    eventwordshortsingular: sport.eventWord.short.singular,
    eventwordshortplural: sport.eventWord.short.plural,
    eventwordlongsingular: sport.eventWord.long.singular,
    eventwordlongplural: sport.eventWord.long.plural,
    defaultpopularityscore: sport.defaultPopularityScore,
    clocktype: sport.clockType,
    baseperiods: sport.basePeriods,
    extraperiods: sport.extraPeriods,
    unsupportedperiods: sport.unsupportedPeriods || [],
    updatedat: new Date().toISOString()
  };
}


/**
 * Synchronizes sports data from SGO API to the sgo_sports table
 * Fetches data from API, transforms it, and upserts to Supabase
 */
export async function syncSGOSports(): Promise<void> {
  try {
    // Check if sports sync is enabled
    if (process.env.ENABLE_SPORTS_SYNC === 'false') {
      console.log('⏭️  Sports sync disabled (ENABLE_SPORTS_SYNC=false)');
      return;
    }

    console.log('Starting SGO sports synchronization...');

    // Fetch sports data from SGO API
    const sportsResponse = await fetchSports();
    
    if (!sportsResponse.data || sportsResponse.data.length === 0) {
      console.log('No sports data received from SGO API');
      return;
    }

    // Transform the data to match our table schema
    const transformedSports = sportsResponse.data.map(transformSportData);
    
    console.log(`Transformed ${transformedSports.length} sports records for database insertion`);

    // Upsert data to Supabase (insert or update if sportID already exists)
    const { data, error } = await supabase
      .from('sgo_sports')
      .upsert(transformedSports, {
        onConflict: 'sportid',
        ignoreDuplicates: false
      })
      .select();

    if (error) {
      throw new Error(`Supabase upsert failed: ${error.message}`);
    }

    console.log(`Successfully synchronized ${data?.length || transformedSports.length} sports records to database`);
    
    // Log some details about what was synced
    const sportIds = transformedSports.map(s => s.sportID).join(', ');
    console.log(`Synced sports: ${sportIds}`);

  } catch (error) {
    console.error('SGO sports synchronization failed:', error);
    throw error;
  }
}

export default syncSGOSports;

// Allow this file to be run directly as a script
if (require.main === module) {
  syncSGOSports()
    .then(() => {
      console.log('✅ Sports sync completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Sports sync failed:', error);
      process.exit(1);
    });
}

import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SGO_API_KEY = process.env.SGO_API_KEY;
const SGO_BASE_URL = 'https://api.sportsgameodds.com/v2';
console.log('[DEBUG] Loaded SGO_API_KEY:', process.env.SGO_API_KEY);


if (!SGO_API_KEY) {
  throw new Error('Missing required environment variable: SGO_API_KEY');
}

// Interface for the Sports API response structure
export interface SGOSportResponse {
  success: boolean;
  data: SGOSport[];
}

export interface SGOSport {
  sportID: string;
  enabled: boolean;
  hasMeaningfulHomeAway: boolean;
  name: string;
  shortName: string;
  prefers3WayMoneyline?: boolean;
  pointWord: {
    short: {
      singular: string;
      plural: string;
    };
    long: {
      singular: string;
      plural: string;
    };
  };
  eventWord: {
    short: {
      singular: string;
      plural: string;
    };
    long: {
      singular: string;
      plural: string;
    };
  };
  defaultPopularityScore: number;
  clockType: string;
  basePeriods: string[];
  extraPeriods: string[];
  unsupportedPeriods?: string[];
}

/**
 * Fetches sports data from the SportsGameOdds API
 * @returns Promise<SGOSportResponse> - The sports data response
 */
export async function fetchSports(): Promise<SGOSportResponse> {
  try {
    console.log('Fetching sports data from SGO API...');
    
    const response = await axios.get(`${SGO_BASE_URL}/sports`, {
      headers: {
        'x-api-key': SGO_API_KEY,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });

    if (!response.data || !response.data.success) {
      throw new Error(`SGO API returned unsuccessful response: ${JSON.stringify(response.data)}`);
    }

    console.log(`Successfully fetched ${response.data.data?.length || 0} sports from SGO API`);
    return response.data;

  } catch (error) {
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.error || error.message;
      const statusCode = error.response?.status;
      throw new Error(`SGO API request failed (${statusCode}): ${errorMessage}`);
    }
    
    throw new Error(`Failed to fetch sports data: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export default fetchSports;

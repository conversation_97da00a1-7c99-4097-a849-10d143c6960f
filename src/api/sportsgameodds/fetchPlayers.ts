import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SGO_API_KEY = process.env.SGO_API_KEY;
const SGO_BASE_URL = 'https://api.sportsgameodds.com/v2';

if (!SGO_API_KEY) {
  throw new Error('Missing required environment variable: SGO_API_KEY');
}

// Interface for the Players API response structure
export interface SGOPlayerResponse {
  success: boolean;
  data: SGOPlayer[];
}

export interface SGOPlayer {
  playerID: string;
  firstName?: string;
  lastName?: string;
  name: string;
  sportID: string;
  leagueID: string;
  teamID: string;
  jerseyNumber?: number;
  position?: string;
}

/**
 * Fetches players data for a specific team from the SportsGameOdds API
 * @param teamID - The team ID to fetch players for
 * @returns Promise<SGOPlayerResponse> - The players data response
 */
export async function fetchPlayers(teamID: string): Promise<SGOPlayerResponse> {
  try {
    const params = new URLSearchParams({
      teamID
    });

    console.log(`Fetching players for team ${teamID}...`);
    
    const response = await axios.get(`${SGO_BASE_URL}/players?${params.toString()}`, {
      headers: {
        'x-api-key': SGO_API_KEY,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });

    if (!response.data || !response.data.success) {
      throw new Error(`SGO API returned unsuccessful response: ${JSON.stringify(response.data)}`);
    }

    console.log(`Successfully fetched ${response.data.data?.length || 0} players for team ${teamID}`);
    return response.data;

  } catch (error) {
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.error || error.message;
      const statusCode = error.response?.status;
      throw new Error(`SGO API request failed (${statusCode}): ${errorMessage}`);
    }
    
    throw new Error(`Failed to fetch players data for team ${teamID}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export default fetchPlayers;

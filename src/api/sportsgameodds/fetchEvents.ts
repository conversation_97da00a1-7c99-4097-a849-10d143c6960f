import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SGO_API_KEY = process.env.SGO_API_KEY;
const SGO_BASE_URL = 'https://api.sportsgameodds.com/v2';

if (!SGO_API_KEY) {
  throw new Error('Missing required environment variable: SGO_API_KEY');
}

// Interface for the Events API response structure
export interface SGOEventResponse {
  success: boolean;
  data: SGOEvent[];
  nextCursor?: string;
}

export interface SGOEvent {
  eventID: string;
  sportID: string;
  leagueID: string;
  type: string;
  status: {
    startsAt: string;
    displayLong?: string;
    displayShort?: string;
    live?: boolean;
    finalized?: boolean;
    cancelled?: boolean;
    delayed?: boolean;
    oddsPresent?: boolean;
    oddsAvailable?: boolean;
    currentPeriodID?: string;
    previousPeriodID?: string;
  };
  teams?: {
    home?: { 
      teamID: string; 
      score?: number; 
      names?: any; 
      colors?: any; 
    };
    away?: { 
      teamID: string; 
      score?: number; 
      names?: any; 
      colors?: any; 
    };
  };
  results?: Record<string, any>;
  odds?: Record<string, any>;
  info?: {
    seasonID?: string;
    seasonWeek?: string;
  };
}

/**
 * Fetches events data from the SportsGameOdds API with pagination support
 * @param params - Query parameters for the API call
 * @returns Promise<SGOEventResponse> - The events data response
 */
export async function fetchEvents(params: {
  sportID?: string;
  leagueID?: string;
  oddsAvailable?: boolean;
  limit?: number;
  cursor?: string;
  startsAtGte?: string;
  startsAtLte?: string;
}): Promise<SGOEventResponse> {
  try {
    const queryParams = new URLSearchParams();
    
    if (params.sportID) queryParams.set('sportID', params.sportID);
    if (params.leagueID) queryParams.set('leagueID', params.leagueID);
    if (params.oddsAvailable) queryParams.set('oddsAvailable', 'true');
    if (params.limit) queryParams.set('limit', params.limit.toString());
    if (params.cursor) queryParams.set('cursor', params.cursor);
    if (params.startsAtGte) queryParams.set('startsAt>=', params.startsAtGte);
    if (params.startsAtLte) queryParams.set('startsAt<=', params.startsAtLte);

    console.log(`Fetching events with params: ${queryParams.toString()}`);
    
    const response = await axios.get(`${SGO_BASE_URL}/events?${queryParams.toString()}`, {
      headers: {
        'x-api-key': SGO_API_KEY,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });

    if (!response.data || !response.data.success) {
      throw new Error(`SGO API returned unsuccessful response: ${JSON.stringify(response.data)}`);
    }

    console.log(`Successfully fetched ${response.data.data?.length || 0} events from SGO API`);
    return response.data;

  } catch (error) {
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.error || error.message;
      const statusCode = error.response?.status;
      throw new Error(`SGO API request failed (${statusCode}): ${errorMessage}`);
    }
    
    throw new Error(`Failed to fetch events data: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Fetches all events with automatic pagination handling
 * @param params - Query parameters for the API call
 * @returns Promise<SGOEvent[]> - All events across all pages
 */

export async function fetchAllEvents(params: {
  sportID?: string;
  leagueID?: string;
  oddsAvailable?: boolean;
  limit?: number;
  startsAtGte?: string;
  startsAtLte?: string;
}): Promise<SGOEvent[]> {
  const allEvents: SGOEvent[] = [];
  let cursor: string | undefined;
  let hasMore = true;

  while (hasMore) {
    const response = await fetchEvents({ ...params, cursor });
    
    if (response.data && response.data.length > 0) {
      allEvents.push(...response.data);
    }
    
    cursor = response.nextCursor;
    hasMore = !!cursor;
  }

  console.log(`Fetched total of ${allEvents.length} events`);
  return allEvents;
}

export default fetchEvents;

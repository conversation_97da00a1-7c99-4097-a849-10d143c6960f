import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SGO_API_KEY = process.env.SGO_API_KEY;
const SGO_BASE_URL = 'https://api.sportsgameodds.com/v2';

if (!SGO_API_KEY) {
  throw new Error('Missing required environment variable: SGO_API_KEY');
}

// Interface for the Leagues API response structure
export interface SGOLeagueResponse {
  success: boolean;
  data: SGOLeague[];
}

export interface SGOLeague {
  leagueID: string;
  sportID: string;
  enabled: boolean;
  name: string;
  shortName: string;
}

/**
 * Fetches leagues data from the SportsGameOdds API
 * @returns Promise<SGOLeagueResponse> - The leagues data response
 */
export async function fetchLeagues(): Promise<SGOLeagueResponse> {
  try {
    console.log('Fetching leagues data from SGO API...');
    
    const response = await axios.get(`${SGO_BASE_URL}/leagues`, {
      headers: {
        'x-api-key': SGO_API_KEY,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });

    if (!response.data || !response.data.success) {
      throw new Error(`SGO API returned unsuccessful response: ${JSON.stringify(response.data)}`);
    }

    console.log(`Successfully fetched ${response.data.data?.length || 0} leagues from SGO API`);
    return response.data;

  } catch (error) {
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.error || error.message;
      const statusCode = error.response?.status;
      throw new Error(`SGO API request failed (${statusCode}): ${errorMessage}`);
    }
    
    throw new Error(`Failed to fetch leagues data: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export default fetchLeagues;

import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SGO_API_KEY = process.env.SGO_API_KEY;
const SGO_BASE_URL = 'https://api.sportsgameodds.com/v2';

if (!SGO_API_KEY) {
  throw new Error('Missing required environment variable: SGO_API_KEY');
}

// Interface for the Teams API response structure
export interface SGOTeamResponse {
  success: boolean;
  data: SGOTeam[];
  nextCursor?: string;
}

export interface SGOTeam {
  teamID: string;
  sportID: string;
  leagueID: string;
  names: {
    short?: string;
    medium?: string;
    long?: string;
  };
  nickname?: string;
  location?: string;
}

/**
 * Fetches teams data for a specific league from the SportsGameOdds API
 * Handles pagination using nextCursor
 * @param leagueID - The league ID to fetch teams for
 * @param cursor - Optional cursor for pagination
 * @returns Promise<SGOTeamResponse> - The teams data response
 */
export async function fetchTeamsForLeague(leagueID: string, cursor?: string): Promise<SGOTeamResponse> {
  try {
    const params = new URLSearchParams({
      leagueID,
      limit: '100'
    });
    
    if (cursor) {
      params.append('cursor', cursor);
    }

    console.log(`Fetching teams for league ${leagueID}${cursor ? ` (cursor: ${cursor})` : ''}...`);
    
    const response = await axios.get(`${SGO_BASE_URL}/teams?${params.toString()}`, {
      headers: {
        'x-api-key': SGO_API_KEY,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });

    if (!response.data || !response.data.success) {
      throw new Error(`SGO API returned unsuccessful response: ${JSON.stringify(response.data)}`);
    }

    console.log(`Successfully fetched ${response.data.data?.length || 0} teams for league ${leagueID}`);
    return response.data;

  } catch (error) {
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.error || error.message;
      const statusCode = error.response?.status;
      throw new Error(`SGO API request failed (${statusCode}): ${errorMessage}`);
    }
    
    throw new Error(`Failed to fetch teams data for league ${leagueID}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Fetches all teams for a specific league, handling pagination automatically
 * @param leagueID - The league ID to fetch teams for
 * @returns Promise<SGOTeam[]> - All teams for the league
 */
export async function fetchAllTeamsForLeague(leagueID: string): Promise<SGOTeam[]> {
  const allTeams: SGOTeam[] = [];
  let cursor: string | undefined;
  let hasMore = true;

  while (hasMore) {
    const response = await fetchTeamsForLeague(leagueID, cursor);
    
    if (response.data && response.data.length > 0) {
      allTeams.push(...response.data);
    }
    
    cursor = response.nextCursor;
    hasMore = !!cursor;
  }

  console.log(`Fetched total of ${allTeams.length} teams for league ${leagueID}`);
  return allTeams;
}

export default fetchAllTeamsForLeague;

SUPABASE_URL=https://omprsmzlggidmybnbiiw.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9tcHJzbXpsZ2dpZG15Ym5iaWl3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA5ODQwOTUsImV4cCI6MjA1NjU2MDA5NX0.M2YRu2No62XTzVhRM6F3A-Y3A9Y1azPXhhIfDxhzFIs
SGO_API_KEY=713a0f7f7a2bfa425d19b926b3aca5ac

# Sync job controls (set to false to disable API calls)
ENABLE_SYNC_ON_STARTUP=false
ENABLE_SPORTS_SYNC=true
ENABLE_LEAGUES_SYNC=true
ENABLE_TEAMS_SYNC=true
ENABLE_PLAYERS_SYNC=true
ENABLE_EVENTS_SYNC=true

# Player sync configuration (for testing/API limit protection)
TARGET_TEAM_ID=CINCINNATI_REDS_MLB

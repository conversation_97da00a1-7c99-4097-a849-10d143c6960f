# SportStake App Backend

A Node.js + TypeScript backend for the SportStake application with SportsGameOdds API integration and Supabase database.

## Project Structure

```
/src
  ├── api/sportsgameodds/
  │   ├── fetchSports.ts                      # Fetches /v2/sports from SGO API
  │   ├── fetchLeagues.ts                     # Fetches /v2/leagues from SGO API
  │   ├── fetchTeams.ts                       # Fetches /v2/teams from SGO API (with pagination)
  │   ├── fetchPlayers.ts                     # Fetches /v2/players from SGO API
  │   └── fetchEvents.ts                      # Fetches /v2/events from SGO API (with pagination)
  ├── db/supabaseClient.ts                    # Supabase connection
  ├── jobs/
  │   ├── syncSGOSports.ts                    # Syncs SGO sports data to database
  │   ├── syncSGOLeagues.ts                   # Syncs SGO leagues data to database
  │   ├── syncSGOTeams.ts                     # Syncs SGO teams data to database
  │   ├── syncSGOPlayers.ts                   # Syncs SGO players data to database (MLB only)
  │   └── syncSGOEvents.ts                    # Syncs SGO events data to database (5 tables)
  ├── routes/                                 # Reserved for future API routes
  ├── utils/                                  # Helper functions
  └── index.ts                                # Entry point
```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SGO_API_KEY=your_sportsgameodds_api_key

# Sync job controls (set to false to disable API calls)
ENABLE_SYNC_ON_STARTUP=false
ENABLE_SPORTS_SYNC=true
ENABLE_LEAGUES_SYNC=true
ENABLE_TEAMS_SYNC=true
ENABLE_PLAYERS_SYNC=true
ENABLE_EVENTS_SYNC=true

# Player sync configuration (for testing/API limit protection)
TARGET_TEAM_ID=CINCINNATI_REDS_MLB
```

## Database Schema

The application expects the following tables in Supabase:

### sgo_sports table

```sql
CREATE TABLE sgo_sports (
  sport_id TEXT PRIMARY KEY,
  enabled BOOLEAN NOT NULL,
  has_meaningful_home_away BOOLEAN NOT NULL,
  name TEXT NOT NULL,
  short_name TEXT NOT NULL,
  point_word_short_singular TEXT NOT NULL,
  point_word_short_plural TEXT NOT NULL,
  point_word_long_singular TEXT NOT NULL,
  point_word_long_plural TEXT NOT NULL,
  event_word_short_singular TEXT NOT NULL,
  event_word_short_plural TEXT NOT NULL,
  event_word_long_singular TEXT NOT NULL,
  event_word_long_plural TEXT NOT NULL,
  default_popularity_score INTEGER NOT NULL,
  clock_type TEXT NOT NULL,
  base_periods JSONB NOT NULL,
  extra_periods JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Events and Odds Tables

```sql
CREATE TABLE events (
  id TEXT PRIMARY KEY,
  sport_id TEXT NOT NULL,
  league_id TEXT NOT NULL,
  type TEXT NOT NULL,
  starts_at TIMESTAMPTZ NOT NULL,
  status_code TEXT NOT NULL,
  is_live BOOLEAN NOT NULL DEFAULT FALSE,
  is_final BOOLEAN NOT NULL DEFAULT FALSE,
  is_cancelled BOOLEAN NOT NULL DEFAULT FALSE,
  odds_present BOOLEAN NOT NULL DEFAULT FALSE,
  odds_available BOOLEAN NOT NULL DEFAULT FALSE,
  current_period TEXT,
  previous_period TEXT,
  season_id TEXT,
  season_week TEXT,
  raw JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE event_participants (
  event_id TEXT NOT NULL,
  participant_kind TEXT NOT NULL,
  participant_id TEXT NOT NULL,
  role TEXT NOT NULL,
  score INTEGER,
  extra JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (event_id, participant_kind, participant_id, role)
);

CREATE TABLE event_period_scores (
  event_id TEXT NOT NULL,
  period_id TEXT NOT NULL,
  home_points INTEGER,
  away_points INTEGER,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (event_id, period_id)
);

CREATE TABLE odds_markets (
  id TEXT PRIMARY KEY,
  event_id TEXT NOT NULL,
  market_name TEXT NOT NULL,
  stat_id TEXT,
  stat_entity_id TEXT,
  period_id TEXT,
  bet_type_id TEXT,
  scoring_supported BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE odds_selections (
  id TEXT PRIMARY KEY,
  market_id TEXT NOT NULL,
  side_id TEXT,
  player_id TEXT,
  team_id TEXT,
  book_odds_available BOOLEAN NOT NULL DEFAULT FALSE,
  fair_odds_available BOOLEAN NOT NULL DEFAULT FALSE,
  book_odds TEXT,
  fair_odds TEXT,
  book_over_under NUMERIC,
  fair_over_under NUMERIC,
  started BOOLEAN NOT NULL DEFAULT FALSE,
  ended BOOLEAN NOT NULL DEFAULT FALSE,
  cancelled BOOLEAN NOT NULL DEFAULT FALSE,
  score TEXT,
  book_info JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### sgo_players table

```sql
CREATE TABLE sgo_players (
  playerid TEXT PRIMARY KEY,
  firstname TEXT NOT NULL DEFAULT '',
  lastname TEXT NOT NULL DEFAULT '',
  name TEXT NOT NULL,
  sportid TEXT NOT NULL,
  leagueid TEXT NOT NULL,
  teamid TEXT NOT NULL,
  position TEXT NOT NULL DEFAULT '',
  jerseynumber INTEGER,
  updatedat TIMESTAMPTZ DEFAULT NOW()
);
```

### sgo_leagues table

```sql
CREATE TABLE sgo_leagues (
  leagueid TEXT PRIMARY KEY,
  sportid TEXT NOT NULL,
  enabled BOOLEAN NOT NULL,
  name TEXT NOT NULL,
  shortname TEXT NOT NULL,
  updatedat TIMESTAMPTZ DEFAULT NOW()
);
```

### sgo_teams table

```sql
CREATE TABLE sgo_teams (
  teamid TEXT PRIMARY KEY,
  sportid TEXT NOT NULL,
  leagueid TEXT NOT NULL,
  shortname TEXT NOT NULL,
  mediumname TEXT NOT NULL,
  longname TEXT NOT NULL,
  nickname TEXT NOT NULL DEFAULT '',
  location TEXT NOT NULL DEFAULT '',
  updatedat TIMESTAMPTZ DEFAULT NOW()
);
```

## Available Scripts

- `npm run build` - Compile TypeScript to JavaScript
- `npm run start` - Run the compiled application
- `npm run dev` - Run in development mode with auto-reload
- `npm run sync-sports` - Run only the sports synchronization job
- `npm run sync-leagues` - Run only the leagues synchronization job
- `npm run sync-teams` - Run only the teams synchronization job (all enabled leagues)
- `npm run sync-teams-league <LEAGUE_ID>` - Run teams sync for a specific league only
- `npm run sync-players` - Run only the players synchronization job (MLB only)
- `npm run sync-players-team <TEAM_ID>` - Run players sync for a specific team only
- `npm run sync-events` - Run only the events synchronization job (all leagues)
- `npm run sync-events-league <LEAGUE_ID>` - Run events sync for a specific league only

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up your environment variables in `.env`

3. Create the database tables in Supabase using the schemas above

4. Run the synchronization jobs in order:
   ```bash
   npm run sync-sports              # First, sync sports data
   npm run sync-leagues             # Then, sync leagues data
   npm run sync-teams               # Then, sync teams data (all enabled leagues)
   npm run sync-players             # Then, sync players data (MLB only)
   npm run sync-events              # Finally, sync events data (all leagues)

   # Or sync specific subsets:
   npm run sync-teams-league MLB    # Sync only MLB teams
   npm run sync-teams-league NBA    # Sync only NBA teams
   npm run sync-players-team CINCINNATI_REDS_MLB  # Sync only one team's players
   npm run sync-events-league MLB                  # Sync only MLB events
   ```

5. Or run the full application:
   ```bash
   npm run dev
   ```

## Sync Control

By default, **sync is disabled on startup** to prevent hitting API limits during development. You can control sync behavior with environment variables:

- `ENABLE_SYNC_ON_STARTUP=false` - Disables all sync jobs when the app starts
- `ENABLE_SPORTS_SYNC=true` - Controls sports sync (individual job or startup)
- `ENABLE_LEAGUES_SYNC=true` - Controls leagues sync (individual job or startup)
- `ENABLE_TEAMS_SYNC=true` - Controls teams sync (individual job or startup)
- `ENABLE_PLAYERS_SYNC=true` - Controls players sync (individual job or startup)
- `ENABLE_EVENTS_SYNC=true` - Controls events sync (individual job or startup)
- `TARGET_TEAM_ID=CINCINNATI_REDS_MLB` - Default team for player sync (API limit protection)

To enable sync on startup, set `ENABLE_SYNC_ON_STARTUP=true` in your `.env` file.

## Sync Job Details

### Sports Sync (`syncSGOSports.ts`)
- **Endpoint**: `GET /v2/sports`
- **Purpose**: Fetches all available sports and their metadata
- **Dependencies**: None (run first)

### Leagues Sync (`syncSGOLeagues.ts`)
- **Endpoint**: `GET /v2/leagues`
- **Purpose**: Fetches all leagues across all sports
- **Dependencies**: None (can run independently)

### Teams Sync (`syncSGOTeams.ts`)
- **Endpoint**: `GET /v2/teams?leagueID=<id>&limit=100`
- **Purpose**: Fetches teams for each enabled league (or specific league)
- **Dependencies**: Requires `sgo_leagues` table to be populated first
- **Features**:
  - Automatic pagination handling with `nextCursor`
  - Processes all enabled leagues from database OR specific target league
  - Batch processing for efficiency
  - Selective sync: `syncSGOTeams('MLB')` or `syncSGOTeams()` for all

### Players Sync (`syncSGOPlayers.ts`)
- **Endpoint**: `GET /v2/players?teamID=<id>`
- **Purpose**: Fetches players for MLB teams only (API limit protection)
- **Dependencies**: Requires `sgo_teams` table with MLB teams populated first
- **Features**:
  - Processes all MLB teams from database OR specific target team
  - Configurable via `TARGET_TEAM_ID` environment variable
  - Robust fallback logic for missing player fields
  - Selective sync: `syncSGOPlayers('CINCINNATI_REDS_MLB')` or `syncSGOPlayers()` for all MLB

### Events Sync (`syncSGOEvents.ts`)
- **Endpoint**: `GET /v2/events?leagueID=<id>&limit=100`
- **Purpose**: Fetches events with odds data and stores in 5 related tables
- **Dependencies**: None (can run independently)
- **Features**:
  - Automatic pagination handling with `nextCursor`
  - Processes all leagues OR specific target league
  - Comprehensive data transformation for 5 database tables
  - Handles events, participants, period scores, odds markets, and selections
  - Selective sync: `syncSGOEvents('MLB')` or `syncSGOEvents()` for all leagues

## Features

- ✅ TypeScript configuration with strict mode
- ✅ SportsGameOdds API integration with proper error handling
- ✅ Supabase client with service role authentication
- ✅ Data transformation from nested API response to flat database schema
- ✅ Upsert functionality to handle data updates
- ✅ Environment variable validation
- ✅ Comprehensive error handling and logging
- ✅ Development scripts with nodemon
- ✅ Multiple sync jobs for sports, leagues, and teams
- ✅ Automatic pagination handling for large datasets
- ✅ CLI support for individual sync job execution
- ✅ Dependency management between sync jobs

## Next Steps

- Add Express.js server setup in `/src/routes`
- Implement API endpoints for frontend consumption
- Add data validation and sanitization
- Implement scheduled jobs for regular data synchronization
- Add unit tests and integration tests
